.nhsuk-file-upload {
  color: $nhsuk-text-color;
  margin-left: nhsuk-spacing(-1);
  max-width: 100%;
  padding: nhsuk-spacing(1);
  @include nhsuk-font($size: 19);

  // The default file upload button in Safari does not support setting a
  // custom font-size. Set `-webkit-appearance` to `button` to drop out of the
  // native appearance so the font-size is set to 19px
  // https://webkit.org/b/224746
  &::-webkit-file-upload-button {
    -webkit-appearance: button;
    color: inherit;
    font: inherit;
  }

  &:focus {
    outline: $nhsuk-focus-width solid $nhsuk-focus-color;
    // Use `box-shadow` to add border instead of changing `border-width`
    // (which changes element size) and since `outline` is already used for
    // the yellow focus state.
    box-shadow: inset 0 0 0 4px $nhsuk-form-border-color;
  }

  // Set "focus-within" to fix https://bugzil.la/1430196 so that component
  // receives focus in Firefox.
  // This can't be set together with `:focus` as all versions of IE fail
  // to recognise `focus-within` and don't set any styles from the block
  // when it's a selector.
  &:focus-within {
    box-shadow: inset 0 0 0 4px $nhsuk-form-border-color;
    outline: $nhsuk-focus-width solid $nhsuk-focus-color;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}
