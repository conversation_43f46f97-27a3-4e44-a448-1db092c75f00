$_button-shadow-size: $nhsuk-border-width-form-element * 2;

.nhsuk-password-input__input {
  // IE 11 and Microsoft Edge comes with its own password reveal function. We
  // want to hide it, so that there aren't two controls presented to the user
  // that do the same thing but aren't in sync with one another. This doesn't
  // affect the function that allows Edge users to toggle password visibility
  // by pressing Alt+F8, which cannot be programmatically disabled.
  &::-ms-reveal {
    display: none;
  }
}

.nhsuk-password-input__toggle {
  // Reduce font weight
  font-weight: normal;

  // Adjust bottom margin to offset shadow size given flex-end alignment
  margin-bottom: $_button-shadow-size;

  // Add margin to top so that button doesn’t obscure input’s focus style
  margin-top: nhsuk-spacing(1);

  // Make full width at smaller breakpoints
  width: 100%;

  // Hide button by default, J<PERSON> removes this attribute
  &[hidden] {
    display: none;
  }

  @include nhsuk-media-query($from: mobile) {
    flex: 1 0 5em;

    // Move spacing from top to the left
    margin-left: nhsuk-spacing(1);
    margin-top: 0;

    // Remove padding to account for height of password input
    padding: 0;

    // Reset width
    width: auto;
  }
}
