.app-u-nowrap {
  white-space: nowrap;
}

.app-u-text-decoration-none {
  text-decoration: none;
}

.app-u-width-one-fifth {
  width: 100% !important;

  @include nhsuk-media-query($from: tablet) {
    width: 20% !important;
  }
}

// Some govuk-frontend JS components dynamically insert content with the
// govuk-visually-hidden class. So we need to shim both namespaces.
.govuk-visually-hidden,
.nhsuk-visually-hidden {
  @include visually-hidden;
}

.app-visually-hidden-until-desktop {
  @include nhsuk-media-query($until: desktop) {
    @include visually-hidden;
  }
}

.app-visually-hidden-from-tablet {
  @include nhsuk-media-query($from: tablet) {
    @include visually-hidden;
  }
}

.app-u-monospace {
  font-family: ui-monospace, monospace;
  font-weight: 450;
}
