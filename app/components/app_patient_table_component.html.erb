<div class="nhsuk-table__panel-with-heading-tab">
  <h3 class="nhsuk-table__heading-tab"><%= t("children", count:) %></h3>

  <%= govuk_table(html_attributes: { class: "nhsuk-table-responsive" }) do |table| %>
    <% table.with_head do |head| %>
      <% head.with_row do |row| %>
        <% row.with_cell(text: "Name and NHS number") %>
        <% row.with_cell(text: "Postcode") %>
        <% row.with_cell(text: "School") %>
        <% row.with_cell(text: "Date of birth") %>
      <% end %>
    <% end %>

    <% table.with_body do |body| %>
      <% patients.each do |patient| %>
        <% body.with_row do |row| %>
          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Name and NHS number</span>

            <span>
              <% if can_link_to?(patient) %>
                <%= link_to patient.full_name, patient_path(patient) %>
              <% else %>
                <%= patient.full_name %>
              <% end %>

              <br />
              <span class="nhsuk-u-secondary-text-color nhsuk-u-font-size-16">
                <%= helpers.patient_nhs_number(patient) %>
              </span>

              <% unless can_link_to?(patient) %>
                <br />
                <span class="nhsuk-u-secondary-text-color">
                  Child has moved out of the area
                </span>
              <% end %>
            </span>
          <% end %>

          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Postcode</span>
            <%= patient.address_postcode unless patient.restricted? %>
          <% end %>

          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">School</span>
            <%= helpers.patient_school(patient) %>
          <% end %>

          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Date of birth</span>
            <%= patient.date_of_birth.to_fs(:long) %>
          <% end %>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
</div>
