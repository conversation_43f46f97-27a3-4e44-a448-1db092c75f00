<%= if patient_session.status(programme:).in? %w[
                                               vaccinated
                                               unable_to_vaccinate
                                             ]
     render AppOutcomeBannerComponent.new(
       patient_session:,
       programme:,
       current_user:,
     )
   else
     render AppSimpleStatusBannerComponent.new(patient_session:, programme:)
   end %>

<%= render AppPatientCardComponent.new(patient) %>

<% if display_gillick_assessment_card? %>
  <%= render AppCardComponent.new do |c| %>
    <% c.with_heading { "Gillick assessment" } %>
    <% if gillick_assessment %>
      <% if gillick_assessment.gillick_competent? %>
        <p class="app-status app-status--aqua-green">
          <svg class="nhsuk-icon nhsuk-icon__tick" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M18.4 7.8l-8.5 8.4L5.6 12" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round"></path>
          </svg>
          Child assessed as <PERSON><PERSON> competent
        </p>
      <% else %>
        <p class="app-status app-status--red">
          <svg class="nhsuk-icon nhsuk-icon__cross" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M18.6 6.5c.5.5.5 1.5 0 2l-4 4 4 4c.5.6.5 1.4 0 2-.4.4-.7.4-1 .4-.5 0-.9 0-1.2-.3l-3.9-4-4 4c-.3.3-.5.3-1 .3a1.5 1.5 0 0 1-1-2.4l3.9-4-4-4c-.5-.5-.5-1.4 0-2 .6-.7 1.5-.7 2.2 0l3.9 3.9 4-4c.6-.6 1.4-.6 2 0Z" fill="currentColor"></path>
          </svg>
          Child assessed as not Gillick competent
        </p>
      <% end %>

      <% if (notes = gillick_assessment.notes).present? %>
        <p class="nhsuk-body"><%= notes %></p>
      <% end %>

      <% if helpers.policy(gillick_assessment).edit? %>
        <p class="nhsuk-body">
          <%= govuk_button_link_to "Edit Gillick competence",
                                   edit_session_patient_programme_gillick_assessment_path(
                                     session,
                                     patient,
                                     programme,
                                   ),
                                   secondary: true,
                                   class: "nhsuk-u-margin-bottom-0" %>
        </p>
      <% end %>
    <% elsif gillick_assessment_can_be_recorded? %>
      <p class="nhsuk-body">
        <%= govuk_button_link_to "Assess Gillick competence",
                                 edit_session_patient_programme_gillick_assessment_path(
                                   session,
                                   patient,
                                   programme,
                                 ),
                                 secondary: true,
                                 class: "nhsuk-u-margin-bottom-0" %>
      </p>
    <% end %>
  <% end %>
<% end %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "Consent" } %>
  <%= render AppConsentComponent.new(patient_session:, programme:) %>
<% end %>

<% if display_health_questions? %>
  <%= render AppHealthAnswersCardComponent.new(
        consents,
        heading: "All answers to health questions",
      ) %>
<% end %>

<% if patient.triages.exists?(programme:) %>
  <%= render AppCardComponent.new do |c| %>
    <% c.with_heading { "Triage notes" } %>
    <%= render AppTriageNotesComponent.new(patient_session:, programme:) %>
  <% end %>
<% end %>

<% if helpers.policy(Triage).create? && patient.triage_status(programme:).required? %>
  <%= render AppCardComponent.new do %>
    <%= render AppTriageFormComponent.new(
          patient_session:,
          programme:,
          url: session_patient_programme_triages_path(
            session,
            patient,
            programme,
            @triage
          ),
          triage: @triage,
          legend: :bold,
        ) %>
  <% end %>
<% end %>

<% vaccination_records.find_each do |vaccination_record| %>
  <%= render AppCardComponent.new do |c| %>
    <% c.with_heading { "Vaccination details" } %>
    <%= render AppVaccinationRecordSummaryComponent.new(vaccination_record, current_user:) %>

    <div class="app-button-group">
      <% if helpers.policy(vaccination_record).edit? %>
        <%= govuk_button_to "Edit vaccination record",
                            programme_vaccination_record_path(programme, vaccination_record),
                            method: :put, secondary: true %>
      <% end %>

      <% if helpers.policy(vaccination_record).destroy? %>
        <%= govuk_link_to "Delete vaccination record",
                          destroy_programme_vaccination_record_path(
                            programme,
                            vaccination_record,
                            return_to: session_patient_programme_path(patient_id: patient.id),
                          ) %>
      <% end %>
    </div>
  <% end %>
<% end %>

<% if helpers.policy(VaccinationRecord).create? %>
  <%= render AppVaccinateFormComponent.new(vaccinate_form) %>
<% end %>
