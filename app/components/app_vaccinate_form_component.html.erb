<%= form_with(
     model: vaccinate_form,
     url:,
     method: :post,
     class: "nhsuk-card",
     builder: GOVUKDesignSystemFormBuilder::FormBuilder,
   ) do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <div class="nhsuk-card__content">
    <h2 class="nhsuk-card__heading nhsuk-heading-m">
      <%= patient.given_name %> has confirmed that they:
    </h2>

    <%= f.govuk_check_boxes_fieldset :feeling_well, multiple: false, legend: nil do %>
      <%= f.govuk_check_box :feeling_well, true, multiple: false, link_errors: true %>
    <% end %>

    <% if vaccinate_form.ask_not_pregnant? %>
      <%= f.govuk_check_boxes_fieldset :not_pregnant, multiple: false, legend: nil do %>
        <%= f.govuk_check_box :not_pregnant, true, multiple: false, link_errors: true %>
      <% end %>
    <% end %>

    <% if vaccinate_form.ask_not_taking_medication? %>
      <%= f.govuk_check_boxes_fieldset :not_taking_medication, multiple: false, legend: nil do %>
        <%= f.govuk_check_box :not_taking_medication, true, multiple: false, link_errors: true %>
      <% end %>
    <% end %>

    <%= f.govuk_check_boxes_fieldset :no_allergies, multiple: false, legend: nil do %>
      <%= f.govuk_check_box :no_allergies, true, multiple: false, link_errors: true %>
    <% end %>

    <%= f.govuk_check_boxes_fieldset :not_already_had, multiple: false, legend: nil do %>
      <%= f.govuk_check_box :not_already_had, true, multiple: false, link_errors: true %>
    <% end %>

    <%= f.govuk_check_boxes_fieldset :knows_vaccination, multiple: false, legend: nil do %>
      <%= f.govuk_check_box :knows_vaccination, true, multiple: false, link_errors: true %>
    <% end %>

    <%= f.govuk_text_area :pre_screening_notes, label: { text: "Pre-screening notes (optional)" } %>

    <hr class="nhsuk-section-break nhsuk-section-break--visible nhsuk-section-break--l">

    <h2 class="nhsuk-card__heading nhsuk-heading-m">
      Is <%= patient.given_name %> ready for their <%= programme.name %> vaccination?
    </h2>

    <%= f.govuk_radio_buttons_fieldset :administered, legend: nil do %>
      <%= f.govuk_radio_button :administered, true, label: { text: "Yes" }, link_errors: true do %>
        <%= f.govuk_collection_radio_buttons :delivery_site,
                                             common_delivery_sites_options,
                                             :value,
                                             :label,
                                             legend: {
                                               text: "Where will the injection be given?",
                                               size: "s",
                                             } %>
      <% end %>
      <%= f.govuk_radio_button :administered, false, label: { text: "No" } %>
    <% end %>

    <%= f.hidden_field :delivery_method, value: delivery_method %>
    <%= f.hidden_field :dose_sequence, value: dose_sequence %>
    <%= f.hidden_field :programme_id, value: programme.id %>

    <%= f.govuk_submit "Continue" %>
  </div>
<% end %>
