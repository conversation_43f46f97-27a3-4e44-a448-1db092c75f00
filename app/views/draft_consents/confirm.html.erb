<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% page_title = "Check and confirm answers" %>

<%= h1 page_title: do %>
  <span class="nhsuk-caption-l">
    <%= @patient.full_name %>
  </span>
  <%= page_title %>
<% end %>

<%= render AppCardComponent.new do |card| %>
  <% card.with_heading { "Consent" } %>
  <%= render AppConsentSummaryComponent.new(@draft_consent, change_links: {
                                                              response: wizard_path("agree"),
                                                              route: wizard_path(@draft_consent.via_self_consent? ? "who" : "route"),
                                                            }) %>
<% end %>

<%= render AppCardComponent.new do |card| %>
  <% card.with_heading { "Child" } %>
  <%= render AppConsentPatientSummaryComponent.new(@draft_consent) %>
<% end %>

<% if (parent_relationship = @draft_consent.parent_relationship).present? %>
  <%= render AppParentCardComponent.new(parent_relationship:, change_links: {
                                          name: wizard_path("who"),
                                          relationship: wizard_path("who"),
                                          email: wizard_path("parent-details"),
                                          phone: wizard_path("parent-details"),
                                        }) %>
<% end %>

<% if @draft_consent.response_given? %>
  <%= render AppHealthAnswersCardComponent.new(@draft_consent, heading: "Health questions") %>
<% end %>

<% if @draft_consent.response_given? && @triage&.status.present? %>
  <%= render AppCardComponent.new do |card| %>
    <% card.with_heading { "Triage" } %>
    <%= govuk_summary_list do |summary_list|
          summary_list.with_row do |row|
            row.with_key { "Status" }
            row.with_value { @triage.status.humanize }
            row.with_action(text: "Change", href: wizard_path(:triage), visually_hidden_text: "triage status")
          end
        
          summary_list.with_row do |row|
            row.with_key { "Triage notes" }
            if @triage.notes.present?
              row.with_value { @triage.notes }
              row.with_action(text: "Change", href: wizard_path(:triage), visually_hidden_text: "triage notes")
            else
              row.with_value { @triage.notes.presence || govuk_link_to("Enter triage notes", wizard_path(:triage)) }
            end
          end
        end %>
  <% end %>
<% end %>

<%= govuk_button_to "Confirm", wizard_path, method: :put, prevent_double_click: true %>
