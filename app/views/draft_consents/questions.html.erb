<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% page_title = "Health questions" %>

<%= h1 page_title: do %>
  <span class="nhsuk-caption-l">
    <%= @patient.full_name %>
  </span>
  <%= page_title %>
<% end %>

<%= form_with model: @draft_consent, url: wizard_path, method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <% @draft_consent.health_answers.each_with_index do |health_answer, index| %>
    <%= f.fields_for "question_#{index}", health_answer do |ff| %>
      <%= ff.govuk_radio_buttons_fieldset(:question,
                                          legend: { size: "s", text: health_answer.question },
                                          hint: { text: health_answer.hint }) do %>
        <%= ff.govuk_radio_button :response, "yes",
                                  label: { text: "Yes" },
                                  link_errors: true do %>
          <%= ff.govuk_text_area :notes,
                                 label: { text: "Give details" } %>
        <% end %>
        <%= ff.govuk_radio_button :response, "no",
                                  label: { text: "No" } %>
      <% end %>
    <% end %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
