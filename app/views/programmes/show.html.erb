<% content_for :page_title, "#{@programme.name} – Overview" %>

<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("programmes.index.title"), href: programmes_path },
                                        ]) %>
<% end %>

<h1 class="nhsuk-heading-l"><%= @programme.name %></h1>

<%= render AppProgrammeNavigationComponent.new(@programme, active: :overview) %>

<%= govuk_button_to "Download vaccination report", programme_vaccination_reports_path(@programme), secondary: true, class: "nhsuk-u-margin-bottom-5" %>

<%= render AppProgrammeStatsComponent.new(programme: @programme) %>
<%= render AppConsentRefusedTableComponent.new(@consents) %>
