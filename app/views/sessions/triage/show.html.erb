<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("sessions.index.title"), href: sessions_path },
                                          { text: @session.location.name, href: session_path(@session) },
                                        ]) %>
<% end %>

<%= render "sessions/header" %>

<div class="nhsuk-grid-row">
  <div class="app-grid-column-filters">
    <%= render AppSearchComponent.new(
          form: @form,
          url: session_triage_path(@session),
          triage_statuses: %w[safe_to_vaccinate do_not_vaccinate delay_vaccination required],
          year_groups: @session.year_groups,
        ) %>
  </div>

  <div class="app-grid-column-results">
    <%= render AppSearchResultsComponent.new(@pagy) do %>
      <% @patient_sessions.each do |patient_session| %>
        <%= render AppPatientSessionSearchResultCardComponent.new(patient_session, context: :triage) %>
      <% end %>
    <% end %>
  </div>
</div>
