<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("school_moves.index.title"), href: school_moves_path },
                                        ]) %>
<% end %>

<% page_title = "Review school move" %>
<%= h1 page_title: do %>
  <span class="nhsuk-caption-l">
    <%= @patient.full_name %>
  </span>
  <%= page_title %>
<% end %>

<div class="nhsuk-grid-row">
  <div class="nhsuk-grid-column-one-half">
    <%= render AppCardComponent.new(colour: "blue") do |card| %>
      <% card.with_heading { "#{@school_move.human_enum_name(:source)} record" } %>
      <%= render AppChildSummaryComponent.new(@patient_with_changes) %>
    <% end %>
  </div>

  <div class="nhsuk-grid-column-one-half">
    <%= render AppCardComponent.new(colour: "blue") do |card| %>
      <% card.with_heading { "Child record" } %>
      <%= render AppChildSummaryComponent.new(@patient) %>
    <% end %>
  </div>
</div>

<%= form_with(
      model: @form,
      url: school_move_path(@school_move),
      method: :patch,
      class: "nhsuk-u-width-two-thirds",
    ) do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_radio_buttons_fieldset :action, legend: { text: "Update the child’s record with this new information?" } do %>
    <%= f.govuk_radio_button :action, :confirm, label: { text: "Update record with new school" }, link_errors: true %>
    <%= f.govuk_radio_button :action, :ignore, label: { text: "Ignore new information" } %>
  <% end %>

  <%= f.govuk_submit "Update child record" %>
<% end %>
