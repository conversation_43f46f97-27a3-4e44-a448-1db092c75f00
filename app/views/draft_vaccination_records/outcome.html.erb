<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% editing = @draft_vaccination_record.editing? || @draft_vaccination_record.outcome.present? %>

<% title = editing ? "Vaccination outcome" : "Why was the #{@programme.name} vaccination not given?" %>
<% content_for :page_title, title %>

<%= form_with model: @draft_vaccination_record, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= f.govuk_radio_buttons_fieldset :outcome,
                                     caption: { text: @patient.full_name, size: "l" },
                                     legend: { size: "l", tag: "h1", text: title } do %>

    <% if editing %>
      <%= f.govuk_radio_button :outcome, "administered",
                               label: { text: "Vaccinated" } %>
    <% end %>

    <%= f.govuk_radio_button :outcome, "already_had",
                             label: { text: "They have already had the vaccine" } %>
    <%= f.govuk_radio_button :outcome, "contraindications",
                             label: { text: "They had contraindications" } %>
    <%= f.govuk_radio_button :outcome, "refused",
                             label: { text: "They refused it" } %>
    <%= f.govuk_radio_button :outcome, "not_well",
                             label: { text: "They were not well enough" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
