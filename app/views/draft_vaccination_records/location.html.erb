<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% title = "Where was the #{@programme.name} vaccination given?" %>
<% content_for :page_title, title %>

<%= form_with model: @draft_vaccination_record, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= f.govuk_radio_buttons_fieldset :location_name,
                                     caption: { text: @patient.full_name, size: "l" },
                                     legend: { size: "l", tag: "h1",
                                               text: title } do %>

    <% @locations.each do |location| %>
      <%= f.govuk_radio_button :location_name, "#{location.name}, #{format_address_single_line(location)}",
                               label: { text: location.name },
                               hint: { text: format_address_single_line(location) } %>
    <% end %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
