<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% if @draft_vaccination_record.editing? %>
  <% page_title = "Edit vaccination record" %>
  <%= h1 page_title do %>
    <span class="nhsuk-caption-l">
      <%= @patient.full_name %>
    </span>
    <%= page_title %>
  <% end %>
<% else %>
  <%= h1 "Check and confirm" %>
<% end %>

<% change_links = {
     batch: wizard_path("batch"),
     delivery_method: wizard_path("delivery"),
     delivery_site: wizard_path("delivery"),
     location: @draft_vaccination_record.wizard_steps.include?(:location) ? wizard_path("location") : nil,
     notes: wizard_path("notes"),
     outcome: @draft_vaccination_record.wizard_steps.include?(:outcome) ? wizard_path("outcome") : nil,
     performed_at: wizard_path("date-and-time"),
   } %>

<% show_notes = @draft_vaccination_record.editing? %>

<% if @draft_vaccination_record.administered? %>
  <%= render AppCardComponent.new do |card| %>
    <% card.with_heading { "Vaccination details" } %>
    <%= render AppVaccinationRecordSummaryComponent.new(
          @draft_vaccination_record,
          current_user:,
          change_links:,
          show_notes:,
        ) %>
  <% end %>
<% else %>
  <%= render AppWarningCalloutComponent.new(heading: "Vaccination was not given") do %>
    <%= render AppVaccinationRecordSummaryComponent.new(
          @draft_vaccination_record,
          current_user:,
          change_links:,
          show_notes:,
        ) %>
  <% end %>
<% end %>

<%= form_with model: @draft_vaccination_record, url: wizard_path, method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <% if @draft_vaccination_record.editing? %>
    <%= f.govuk_submit "Save changes" %>
  <% else %>
    <div class="nhsuk-card">
      <div class="nhsuk-card__content">
        <%= f.govuk_text_area :notes,
                              label: {
                                text: "Notes (optional)",
                                size: "m",
                                class: "nhsuk-u-margin-bottom-3",
                              },
                              hint: {
                                text: "For example, if the child had a reaction to the vaccine",
                              },
                              rows: 5 %>
      </div>
    </div>

    <%= f.govuk_submit "Confirm" %>
  <% end %>
<% end %>
