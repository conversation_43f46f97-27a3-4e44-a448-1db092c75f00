<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(backlink_path) %>
<% end %>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= h1 t(@consent_form.reason, scope: %i[consent_forms reason_notes title]) %>

  <%= f.govuk_text_area :reason_notes,
                        label: { text: "Give details" + (@consent_form.reason_notes_must_be_provided? ? "" : " (optional)") } %>

  <%= f.govuk_submit "Continue" %>
<% end %>
