<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@header_path, name: "start consent page") %>
<% end %>

<%= h1 "What is your child’s name?" %>

<p>
  Give the name on your child’s birth certificate. If it’s changed, give the
  name held by your child’s GP.
</p>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_text_field :given_name, label: { text: "First name" },
                                      hint: { text: "Or given name" } %>
  <%= f.govuk_text_field :family_name, label: { text: "Last name" },
                                       hint: { text: "Or family name" } %>

  <%= f.govuk_radio_buttons_fieldset :use_preferred_name,
                                     legend: { size: "s",
                                               text: "Do they use a different name in school?" } do %>
    <%= f.govuk_radio_button :use_preferred_name,
                             true,
                             label: { text: "Yes" },
                             link_errors: true do %>
      <%= f.govuk_text_field :preferred_given_name, label: { text: "Preferred first name (optional)" } %>
      <%= f.govuk_text_field :preferred_family_name, label: { text: "Preferred last name (optional)" } %>
    <% end %>
    <%= f.govuk_radio_button :use_preferred_name, false, label: { text: "No" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
