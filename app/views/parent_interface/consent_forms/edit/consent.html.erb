<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(backlink_path) %>
<% end %>

<% title = t("consent_forms.consent.title.#{@consent_form.programmes.first.type}") %>
<% content_for :page_title, title %>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= f.govuk_radio_buttons_fieldset(:response,
                                     legend: { size: "l", text: title, tag: "h1" },
                                     hint: { text: t("consent_forms.consent.hint.#{@consent_form.programmes.first.type}") }) do %>
    <%= f.govuk_radio_button :response, "given",
                             label: { text: t("consent_forms.consent.i_agree.#{@consent_form.programmes.first.type}") },
                             link_errors: true %>
    <% if @consent_form.programmes.count > 1 %>
      <%= f.govuk_radio_button :response, "given_one",
                               label: { text: "I agree to them having one of the vaccinations" } do %>
        <%= f.govuk_radio_buttons_fieldset :chosen_vaccine,
                                           legend: {
                                             size: "s",
                                             text: "Which vaccinations do you give consent for?",
                                           } do %>
          <% @consent_form.programmes.each do |programme| %>
            <%= f.govuk_radio_button :chosen_vaccine, programme.type,
                                     label: { text: programme.name } %>
          <% end %>
        <% end %>
      <% end %>
    <% end %>

    <%= f.govuk_radio_button :response, "refused", label: { text: "No" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
