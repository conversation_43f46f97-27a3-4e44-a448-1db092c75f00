<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(new_import_path, name: "import") %>
<% end %>

<% title = "Import vaccination records" %>

<% content_for :page_title, title %>

<%= form_with model: @immunisation_import, url: immunisation_imports_path do |f| %>
  <%= f.govuk_error_summary %>

  <h1 class="nhsuk-heading-l"><%= title %></h1>

  <p>You can import vaccination records by uploading:</p>

  <ul class="nhsuk-list nhsuk-list--bullet">
    <li>a Mavis CSV file</li>
    <li>a SystmOne file</li>
  </ul>

  <%= render AppImportFormatDetailsComponent.new(import: @immunisation_import) %>

  <%= f.govuk_file_field :csv, label: { text: "Upload file", size: "m" } %>

  <%= f.govuk_submit %>
<% end %>
