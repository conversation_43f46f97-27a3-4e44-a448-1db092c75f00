{"Version": "2012-10-17", "Statement": [{"Sid": "Statement1", "Effect": "Allow", "Action": ["backup:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backup:<PERSON><PERSON>BackupSelection", "backup:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backup:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backup:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backup:<PERSON><PERSON><PERSON>estoreTestingPlan", "backup:<PERSON><PERSON><PERSON><PERSON><PERSON>aultAccessPolicy", "backup:<PERSON><PERSON>BackupPlan", "backup:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backup:<PERSON>dateReportPlan", "backup:UpdateRestoreTestingPlan", "backup-storage:MountCapsule", "cloudformation:CreateResource", "kms:<PERSON><PERSON><PERSON><PERSON><PERSON>", "kms:CreateGrant", "kms:C<PERSON><PERSON><PERSON>", "kms:Decrypt", "kms:DescribeKey", "kms:EnableKeyRotation", "kms:GenerateDataKey", "kms:PutKeyPolicy", "kms:RetireGrant", "kms:ScheduleKeyDeletion"], "Resource": ["*"]}]}