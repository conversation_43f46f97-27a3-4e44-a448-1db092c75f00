name: Deploy infrastructure
run-name: Deploy infrastructure for ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: Deployment environment
        required: true
        type: choice
        options:
          - qa
          - test
          - preview
          - training
          - production
          - sandbox-alpha
          - sandbox-beta
      image_tag:
        description: Docker image tag to deploy
        required: false
        type: string
  workflow_call:
    inputs:
      environment:
        description: Deployment environment
        required: true
        type: string
      image_tag:
        required: false
        type: string

permissions: {}

concurrency:
  group: deploy-infrastructure-${{ inputs.environment }}

env:
  aws_role: ${{ inputs.environment == 'production'
    && 'arn:aws:iam::************:role/GithubDeployMavisAndInfrastructure'
    || 'arn:aws:iam::************:role/GithubDeployMavisAndInfrastructure' }}
  aws_account_id: ${{ inputs.environment == 'production'
    && '************' || '************' }}

defaults:
  run:
    working-directory: terraform/app

jobs:
  validate-permissions:
    name: Validate permissions
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    outputs:
      policy-mismatch: ${{ steps.compare-permissions.outputs.policy_mismatch }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.image_tag || github.sha }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Compare permissions
        id: compare-permissions
        run: |
          ../scripts/validate-github-actions-policy.sh arn:aws:iam::${{ env.aws_account_id }}:policy/DeployMavisResources ../resources/github_actions_policy.json

  update-permissions:
    name: Update permissions
    runs-on: ubuntu-latest
    needs: validate-permissions
    if: (inputs.environment == 'production' || inputs.environment == 'preview') && needs.validate-permissions.outputs.policy-mismatch == 'true'
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.image_tag || github.sha }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Update IAM policy
        run: |
          ../scripts/update-github-actions-policy.sh arn:aws:iam::${{ env.aws_account_id }}:policy/DeployMavisResources ../resources/github_actions_policy.json

  plan:
    name: Terraform plan
    runs-on: ubuntu-latest
    needs: [validate-permissions, update-permissions]
    if: always() && (needs.validate-permissions.outputs.policy-mismatch != 'true' || needs.update-permissions.result == 'success')
    permissions:
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.image_tag || github.sha }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Set image tag
        run: |
          IMAGE_TAG="${{ inputs.image_tag || github.sha }}"
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
      - name: Login to ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Pull Docker image
        run: |
          set -e
          DOCKER_IMAGE="${{ steps.login-ecr.outputs.registry }}/mavis/webapp:${IMAGE_TAG}"
          docker pull "$DOCKER_IMAGE"
          echo "DOCKER_IMAGE=$DOCKER_IMAGE" >> $GITHUB_ENV
      - name: Extract image digest
        run: |
          set -e
          DOCKER_DIGEST=$(docker inspect --format='{{index .RepoDigests 0}}' "$DOCKER_IMAGE")
          DIGEST="${DOCKER_DIGEST#*@}"
          echo "DIGEST=$DIGEST" >> $GITHUB_ENV
      - name: Install terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.11.4
      - name: Install AWS CLI
        run: sudo snap install --classic aws-cli
      - name: Check if any deployments are running
        run: ../scripts/check-for-running-deployments.sh ${{ inputs.environment }}
      - name: Terraform Plan
        id: plan
        run: |
          set -e
          terraform init -backend-config="env/${{ inputs.environment }}-backend.hcl" -upgrade
          terraform plan -var="image_digest=$DIGEST" -var-file="env/${{ inputs.environment }}.tfvars" \
          -out ${{ runner.temp }}/tfplan | tee ${{ runner.temp }}/tf_stdout
      - name: Validate the changes
        run: |
          set -e
          ../scripts/validate_plan.sh ${{ runner.temp }}/tf_stdout
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: tfplan_infrastructure-${{ inputs.environment }}
          path: ${{ runner.temp }}/tfplan

  apply:
    name: Terraform apply
    runs-on: ubuntu-latest
    needs: plan
    if: always() && needs.plan.result == 'success'
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.image_tag || github.sha }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: tfplan_infrastructure-${{ inputs.environment }}
          path: ${{ runner.temp }}
      - name: Install terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.11.4
      - name: Apply the changes
        run: |
          set -e
          terraform init -backend-config="env/${{ inputs.environment }}-backend.hcl" -upgrade
          terraform apply ${{ runner.temp }}/tfplan
