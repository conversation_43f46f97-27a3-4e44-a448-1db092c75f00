@startuml

title Failed malicious GET of CSRF

autoactivate on

== User is still authenticated with manage.nhs.uk ==

group Malicious update to patient data
    Browser -> evil.site: GET /
    return 200 OK
    note right
        // Malicious script attempts to retrieve a csrf token
        fetch("https://manage.nhs.uk/csrf")
    end note

    Browser -[#red]>x manage.nhs.uk: GET /csrf
    note right #FCC
        Cross-Origin Request Blocked by browser
    end note
end

@enduml
